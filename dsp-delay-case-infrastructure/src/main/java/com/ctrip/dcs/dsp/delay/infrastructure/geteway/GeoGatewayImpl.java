package com.ctrip.dcs.dsp.delay.infrastructure.geteway;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.ctrip.dcs.dsp.delay.infrastructure.dto.GaoDeFutureMonitorDTO;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.basic.map.application.service.interfaces.BaseGpsDTO;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.DcsMapDomainServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.OchGeoServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.trocks.TRocksProviderProxy;
import com.ctrip.dcs.dsp.delay.limit.LbsRateLimiter;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.util.GeoHashUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.dcs.location.application.service.interfaces.dto.ExtendInfoDTO;
import com.ctrip.dcs.location.application.service.message.QueryPredictRouteRequestType;
import com.ctrip.dcs.location.application.service.message.QueryPredictRouteResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.geo.interfaces.dto.BaseLatLngPairDTO;
import com.ctrip.igt.geo.interfaces.dto.GaodeDistanceInfoDTO;
import com.ctrip.igt.geo.interfaces.message.QueryDistanceBatchRequestType;
import com.ctrip.igt.geo.interfaces.message.QueryDistanceBatchResponseType;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.github.benmanes.caffeine.cache.Cache;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 */
@Component
public class GeoGatewayImpl implements GeoGateway {

    private static final Logger logger = LoggerFactory.getLogger(GeoGatewayImpl.class);

    @Autowired
    private OchGeoServiceProxy ochGeoServiceProxy;

    @Autowired
    private TRocksProviderProxy trocksProviderProxy;

    @Autowired
    private Cache<String, String> caffeineCache;

    @Autowired
    private LbsRateLimiter rateLimiter;

    @Autowired
    private DcsMapDomainServiceProxy dcsMapDomainServiceProxy;

    @Resource(name = "gaoDeFutureThreadPool")
    private ExecutorService gaoDeFutureThreadPool;

    @Resource(name = "delayGaoDeFutureThreadPool")
    private ExecutorService delayGaoDeFutureThreadPool;

    @Override
    public List<Route> queryRoutes(Integer cityId, String orderId, List<Position> positions) {
        List<Route> routes = queryRoutes(cityId, positions);
        CompletableFuture.runAsync(() -> {
            queryRoutesNew(cityId, orderId, positions, routes);
        }, gaoDeFutureThreadPool);
        return routes;
    }

    @Override
    public List<Route> queryRoutes(Integer cityId, List<Position> positions) {
        List<Route> routes = Lists.newArrayList();
        List<List<Position>> partition = Lists.partition(positions, 99);
        for (List<Position> list : partition) {
            try {
                double acquire = rateLimiter.acquire();
                logger.info("GeoGatewayImpl.queryRoutes", "get token.time:{}", acquire);
                QueryDistanceBatchRequestType request = buildQueryDistanceBatchRequestType(cityId, list);
                QueryDistanceBatchResponseType response = ochGeoServiceProxy.queryDistanceBatch(request);
                routes.addAll(buildRoute(response));
            } catch (Exception e) {
                logger.error("GeoGatewayImpl.queryRoutes", e);
            }
        }
        return routes;
    }

    public void queryRoutesNew(Integer cityId, String userOrderId, List<Position> positions, List<Route> routes) {

        if (CollectionUtils.isEmpty(positions)) {
            return;
        }

        List<Route> result = new ArrayList<>();
        List<CompletableFuture<Route>> futures = new ArrayList<>();

        for (Position position : positions) {
            CompletableFuture<Route> future = CompletableFuture.supplyAsync(() -> {
                Transaction transaction = Cat.newTransaction("queryRoutesNew", cityId.toString());
                try {
                    return queryEstimateRoute(cityId, userOrderId, position);
                } catch (Exception e) {
                    logger.error("GeoGatewayImpl.queryRoutesNew", e);
                    transaction.setStatus(e);
                    return null;
                } finally {
                    transaction.complete();
                }
            }, delayGaoDeFutureThreadPool);
            futures.add(future);
        }

        // 等待所有异步任务完成并收集结果
        for (CompletableFuture<Route> future : futures) {
            try {
                Route route = future.get();
                if (route != null) {
                    result.add(route);
                }
            } catch (Exception e) {
                logger.error("GeoGatewayImpl.queryRoutesNew - future.get() error", e);
            }
        }
        Map<String, Route> collect1 = result.stream().collect(Collectors.toMap(Route::getTimePeriodHash, r -> r, (r1, r2) -> r1));
        Map<String, Route> collect = routes.stream().collect(Collectors.toMap(Route::getHash, r -> r, (r1, r2) -> r1));
        for (Position position : positions) {
            String fromHash = GeoHashUtil.buildGeoHash(position.getFromLongitude(), position.getFromLatitude());
            String toHash = GeoHashUtil.buildGeoHash(position.getToLongitude(), position.getToLatitude());
            String hash = Position.hash(fromHash, toHash);
            String timeHash = Position.timeHash(fromHash, toHash, position.getDepartureTime());
            if (collect.containsKey(hash) && collect1.containsKey(timeHash)) {
                Route realTimeRoute = collect.get(hash);
                Route futureRoute = collect1.get(timeHash);
                logger.info("GeoGatewayImpl.queryRoutesNew", "realTimeRoute:{}, futureRoute:{}", realTimeRoute, futureRoute);
                MetricsUtil.recordValue("query.route.future.success", 1);
                GaoDeFutureMonitorDTO gaoDeFutureMonitorDTO = new GaoDeFutureMonitorDTO();
                gaoDeFutureMonitorDTO.setCityId(String.valueOf(cityId));
                gaoDeFutureMonitorDTO.setOrderId(userOrderId);
                gaoDeFutureMonitorDTO.setDepartureTime(DateUtil.formatDate(position.getDepartureTime(), DateUtil.DATE_FMT));
                gaoDeFutureMonitorDTO.setOrigin(position.getFromHash());
                gaoDeFutureMonitorDTO.setDestination(position.getToHash());
                gaoDeFutureMonitorDTO.setRealTimeDistance(String.valueOf(realTimeRoute.getDistance()));
                gaoDeFutureMonitorDTO.setRealTimeDuration(String.valueOf(realTimeRoute.getDuration()));
                gaoDeFutureMonitorDTO.setFutureDistance(String.valueOf(futureRoute.getDistance()));
                gaoDeFutureMonitorDTO.setFutureDuration(String.valueOf(futureRoute.getDuration()));

            }
        }
    }

    private Route queryEstimateRoute(Integer cityId, String userOrderId, Position position) {
        if (Objects.isNull(position.getDepartureTime())) {
            return null;
        }
        QueryPredictRouteResponseType queryPredictRouteResponseType = dcsMapDomainServiceProxy.queryEstimateRoute(getQueryPredictRouteRequestType(cityId, userOrderId, position));
        if (Objects.isNull(queryPredictRouteResponseType)) {
            return null;
        }
        if (queryPredictRouteResponseType.getResponseResult() == null) {
            return null;
        }
        if (!ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(queryPredictRouteResponseType.getResponseResult().getReturnCode())) {
            return null;
        }

        String fromHash = GeoHashUtil.buildGeoHash(position.getFromLongitude(), position.getFromLatitude());
        String toHash = GeoHashUtil.buildGeoHash(position.getToLongitude(), position.getToLatitude());
        Integer duration = queryPredictRouteResponseType.getDuration();
        Integer distance = queryPredictRouteResponseType.getDistance();
        if (Objects.isNull(duration) || Objects.isNull(distance)) {
            return null;
        }

        String hash = Position.hash(fromHash, toHash);
        String timeHash = Position.timeHash(fromHash, toHash, position.getDepartureTime());

        return new Route(hash, timeHash, distance.doubleValue() / 1000, duration.doubleValue() / 60);
    }


    private static QueryPredictRouteRequestType getQueryPredictRouteRequestType(Integer cityId, String userOrderId, Position position) {
        QueryPredictRouteRequestType routeRequestType = new QueryPredictRouteRequestType();
        BaseGpsDTO from = new BaseGpsDTO();
        from.setLatitude(BigDecimal.valueOf(position.getFromLatitude()));
        from.setLongitude(BigDecimal.valueOf(position.getFromLongitude()));
        from.setCoordType(position.getFromCoordsys());
        from.setCityId(cityId.longValue());
        routeRequestType.setOrigin(from);

        BaseGpsDTO to = new BaseGpsDTO();
        to.setLatitude(BigDecimal.valueOf(position.getToLatitude()));
        to.setLongitude(BigDecimal.valueOf(position.getToLongitude()));
        to.setCoordType(position.getToCoordsys());
        to.setCityId(cityId.longValue());

        routeRequestType.setDestination(to);
        if (Objects.nonNull(position.getDepartureTime())) {
            routeRequestType.setDepartureTime(position.getDepartureTime().getTime());
            ExtendInfoDTO extendInfoDTO = new ExtendInfoDTO();
            extendInfoDTO.setForceGaodeFuture(true);
            routeRequestType.setExtendInfoDTO(extendInfoDTO);
        }
        routeRequestType.setOrderId(userOrderId);
        return routeRequestType;
    }

    @Override
    public Route queryRoute(Long taskId, Position position) {
        try {
            if (Objects.equals(position.getFromHash(), position.getToHash())) {
                return new Route(position.hash(), 0, 0);
            }
            String key = Route.toKey(taskId, position.hash());
            String v = caffeineCache.get(key, k -> (trocksProviderProxy.get(k)));
            if (StringUtils.isBlank(v)) {
                MetricsUtil.recordValue("query.route.cache.null", 1);
                return new Route(position.hash(), Integer.MAX_VALUE, Integer.MAX_VALUE);
            }
            List<String> list = Splitter.on(CommonConstant.PLACEHOLDER).splitToList(v);
            return new Route(position.hash(), Double.valueOf(list.get(0)), Double.valueOf(list.get(1)));
        } catch (Exception e) {
            logger.error("GeoGatewayImpl.queryRoute", e);
        }
        MetricsUtil.recordValue("query.route.cache.null", 1);
        return new Route(position.hash(), Integer.MAX_VALUE, Integer.MAX_VALUE);
    }



    private QueryDistanceBatchRequestType buildQueryDistanceBatchRequestType(Integer cityId, List<Position> list) {
        List<BaseLatLngPairDTO> dtos = Lists.newArrayList();
        for (Position position : list) {
            BaseLatLngPairDTO dto = new BaseLatLngPairDTO();
            dto.setCid(cityId.longValue());
            dto.setCoordType(position.getFromCoordsys());
            dto.setOriginLongitude(BigDecimal.valueOf(position.getFromLongitude()));
            dto.setOriginLatitude(BigDecimal.valueOf(position.getFromLatitude()));
            dto.setDestinationLongitude(BigDecimal.valueOf(position.getToLongitude()));
            dto.setDestinationLatitude(BigDecimal.valueOf(position.getToLatitude()));
            dtos.add(dto);
        }
        QueryDistanceBatchRequestType request = new QueryDistanceBatchRequestType();
        request.setGpsPair(dtos);
        return request;
    }

    private List<Route> buildRoute(QueryDistanceBatchResponseType response) {
        List<Route> list = Lists.newArrayList();
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getResults())) {
            return list;
        }
        for (GaodeDistanceInfoDTO dto : response.getResults()) {
            String fromHash = GeoHashUtil.buildGeoHash(dto.getOriginLongitude().doubleValue(), dto.getOriginLatitude().doubleValue());
            String toHash = GeoHashUtil.buildGeoHash(dto.getDestinationLongitude().doubleValue(), dto.getDestinationLatitude().doubleValue());
            Route route = new Route(Position.hash(fromHash, toHash), (double) dto.getDistance() / 1000, (double) dto.getDuration() / 60);
            list.add(route);
        }
        return list;
    }
}
